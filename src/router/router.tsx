import React, {useMemo} from 'react';
import Main, {workspaceFlattenedMenuList, flattenedMenuList, menus, workspaceMenus} from '../pages';
import {Suspense} from 'react';
import {createHashRouter, Navigate, RouterProvider} from 'react-router-dom';
import urls from '../utils/urls';
import {useAppContext} from '@baidu/bce-react-toolkit';
import {Alert, Loading} from 'acud';
import flags from '../flags';
import {withPermissionHoc} from './WithPermissionHoc';
import {useSelector} from 'react-redux';
import {IAppState} from '@store/index';
import useEnv from '../hooks/useEnv';
import {playgroundInfo} from '@helpers/palyground-info';

const isPrivate = flags.DatabuilderPrivateSwitch;
// 仅在非私有化模式下加载激活页面组件
const Activation = !isPrivate
  ? React.lazy(() => import(/* webpackChunkName: "Activation" */ '@pages/Activation'))
  : null;

// 仅在非私有化模式下加载成都无权限页面组件
const ForbiddenChengdu = !isPrivate
  ? React.lazy(
      () =>
        import(
          /* webpackChunkName: "ForbiddenChengdu" */ '@components/WithoutPermissionPage/ForbiddenChengdu'
        )
    )
  : null;

// 主路由
const mainRouter = flattenedMenuList.map((menu) => ({
  path: menu.key,
  element: <Main menus={menus} component={menu.Component} />,
  privilege: menu.privilege
}));

// playground 用户的主路由 重定向
const playgroundMainRouter = flattenedMenuList.map((menu) => ({
  path: menu.key,
  element: (
    <Navigate
      to={`/workspace/workarea?folderId=${playgroundInfo.folderId}&workspaceId=${playgroundInfo.workspaceId}`}
      replace
    />
  ),
  privilege: menu.privilege
}));

// 创建一个包装器组件来处理懒加载
const LazyComponent = ({component: Component}: {component?: React.ComponentType<any>}) => {
  return (
    Component && (
      <Suspense
        fallback={
          <div className="db-workspace-wrapper">
            <Loading />
          </div>
        }
      >
        {/* <Component /> */}
        <div>
          <Alert
            message="PlayGround 环境是公共环境，请勿上传敏感或关键数据，体验到期后会清空体验数据。"
            type="warning"
            closable
          />
          <Component />
        </div>
      </Suspense>
    )
  );
};

// 工作空间路由 使用二级路由
const workspaceRouter = [
  {
    path: '/workspace',
    id: 'workspace',
    element: <Main menus={workspaceMenus} />,
    children: workspaceFlattenedMenuList.map((menu) => ({
      path: menu.key,
      element: withPermissionHoc(<LazyComponent component={menu.Component} />, menu.privilege),
      privilege: menu.privilege
    }))
  }
];

const RouterComponent = () => {
  const {appState, appDispatch} = useAppContext();
  const availableInChengdu = useSelector((state: IAppState) => state.globalAuthSlice.availableInChengdu);
  const {isPlayGroundUser} = useEnv();
  const hidderChengdu = !isPrivate && !availableInChengdu;

  const router = useMemo(() => {
    const list = hidderChengdu
      ? [
          {
            path: urls.regionInvalid,
            element: <LazyComponent component={ForbiddenChengdu} />
          },
          {
            path: '*',
            element: <Navigate to={urls.regionInvalid} replace />
          }
        ]
      : [
          // 如果是 playground 用户，使用重定向路由，否则使用正常的主路由
          ...(isPlayGroundUser ? playgroundMainRouter : mainRouter),
          ...workspaceRouter,
          // TODO: 需要修改
          {
            path: '*',
            element: <Navigate to={urls.manageWorkspace} replace />
          }
        ];
    return createHashRouter(list);
  }, [appState.isActivated, hidderChengdu, isPlayGroundUser]);

  return <RouterProvider router={router} />;
};

export default RouterComponent;
