/**
 * 公有云环境的【空间内】顶部栏组件
 * 与 WorkspaceOutHeader 的区别：
 * - WorkspaceHeader: 仅用于公有云环境的【空间内】顶部栏
 * - WorkspaceOutHeader: 用于私有化环境的顶部栏，覆盖空间内外所有场景
 * <AUTHOR>
 */

import {FC, useCallback, useMemo} from 'react';
import {Menu, Select, Dropdown, Link} from 'acud';
import {queryWorkspaceList} from '@api/workspace';
import {useNavigate} from 'react-router-dom';
import urls from '@utils/urls';
import {useRegion} from '@hooks/useRegion';
import useEnv from '@hooks/useEnv';
import {useCookieState, useRequest} from 'ahooks';
import useUrlState from '@ahooksjs/use-url-state';
import cx from 'classnames';
import AvatarDropdown from './AvatarDropdown';
import store from '@store/index';

import styles from './index.module.less';
import IconSvg from '@components/IconSvg';
import LogoImg from '@assets/png/logo.png';
import {getWorkspacePrivilege} from '@utils/auth';
import {workspaceMenus} from '@pages/index';
import {Privilege} from '@api/permission/type';

interface IWorkspaceHeaderProps {
  workspaceId: string;
  isDisabled?: boolean;
}

// 最好不要用enum 如果用enum 国际化无法处理
const infoList = [
  {
    name: '财务',
    icon: 'header-billing',
    children: [
      {
        label: '新建工单',
        key: 'https://console.bce.baidu.com/support/#/ticket/#/ticket/create'
      },
      {
        label: '工单列表',
        key: 'https://console.bce.baidu.com/support/#/ticket/#/ticket/list'
      }
    ]
  }
  // {
  //   name: '文档',
  //   icon: 'header-doc',
  //   children: [
  //     {
  //       label: '产品文档',
  //       key: 'https://console.bce.baidu.com/support/#/ticket/#/ticket/list'
  //     }
  //   ]
  // }
];

// 添加logo
// const logoInfo = {
//   logo: <img src={logoSrc} width={150} />,
//   urlConfig: {
//     href: '/databuilder/',
//     target: '_self'
//   }
// };

const RightMenu = ({items}: {items: Array<{label: string; key: string}>}) => (
  <Menu
    onClick={({key}: {key: string}) => {
      window.open(key, '_blank');
    }}
  >
    {items.map((item) => (
      <Menu.Item key={item.key}>{item.label}</Menu.Item>
    ))}
  </Menu>
);

const Header: FC<IWorkspaceHeaderProps> = ({workspaceId, isDisabled}) => {
  const {currentRegion} = useRegion();
  const [, setUrlParams] = useUrlState();

  // 获取用户名 framework也是这么拿的
  const [userName] = useCookieState('bce-login-display-name');
  const navigate = useNavigate();

  const {data, loading} = useRequest(queryWorkspaceList, {
    defaultParams: [{pageSize: 10000}, true],
    onSuccess: (res) => {
      // 首先此Header组件只在「公有云」工作空间内使用，所以 workspaceId 必定存在
      // 如果当前 workspaceId 不在 workspaceList 中，则跳转到空间列表页
      // TODO: 后续改进此请求应在 src/pages/index.tsx 中进行请求拦截，可避免页面因其他请求报错而出现报错提示
      // 同理 私有化 src/components/WorkspaceOutHeader/private-index.tsx 也要调整
      if (!res.result.items.find((item) => item.id === workspaceId)) {
        // 跳转到工作空间列表页
        navigate(urls.manageWorkspace);
      }
    }
  });

  const workspaceList = useMemo(() => {
    return data?.result?.items.map((item) => ({
      label: item.name,
      value: item.id,
      disabled: !item.privileges.includes(Privilege.Browse) || item.status === 'ERROR'
    }));
  }, [data]);

  const onWorkspaceChange = useCallback(
    async (id) => {
      const res = await getWorkspacePrivilege(id);

      store.dispatch({
        type: 'globalAuth/updateWorkspacePermission',
        payload: res
      });

      const menusConfig = workspaceMenus.filter((item) => item.isNavMenu);

      const currentKey = location.hash.split('?')[0].replace('#', '');
      // 1. 当前路径是否有权限
      const currentLinkHasPrivilege = menusConfig.find((item) => item.key === currentKey)?.privilege;

      // 2. 第一条有权限的路径
      // 3. 如果均无权限，工作区路径兜底
      const firstLink = menusConfig.find((item) => res?.[item.privilege])?.key || urls.workArea;

      navigate(`${res?.[currentLinkHasPrivilege] ? currentKey : firstLink}?workspaceId=${id}`);
    },
    [navigate]
  );

  // playground 用户
  const {isPlayGroundUser} = useEnv();

  // 工作空间选择器
  const WorkspaceSelect = useMemo(
    () => (
      <Select
        options={workspaceList}
        loading={loading}
        value={loading ? '' : workspaceId}
        showSearch
        filterOption={(inputValue, option) => (option.label as string).includes(inputValue)}
        disabled={isDisabled}
        onChange={onWorkspaceChange}
        className={styles['workspace-select']}
        style={{width: 130}}
        dropdownMatchSelectWidth={false}
        dropdownClassName={styles['workspace-select-dropdown']}
        dropdownRender={(menu) => (
          <>
            {menu}
            {!isPlayGroundUser && (
              <div className={styles['workspace-select-dropdown-footer']}>
                前往<Link onClick={() => navigate(urls.manageWorkspace)}>全部空间</Link>
              </div>
            )}
          </>
        )}
      />
    ),
    [workspaceList, loading, workspaceId, setUrlParams]
  );

  // 标题信息
  const titleInfo = useMemo(
    () => ({
      title: (
        <div
          className={styles['header-title']}
          onClick={() => !isPlayGroundUser && navigate('/manage-workspace')}
        >
          <img src={LogoImg} width={111} />
          <div className={styles['header-title-text']}>DataBuilder</div>
        </div>
      )
    }),
    [navigate]
  );

  // 退出登录
  const accountLogout = () => {
    // 获取framework退出登录按钮
    const exitElement = document.querySelector(
      '#bce-content .header .content #user-nav .iam-antd-global :nth-child(4) :nth-child(2)'
    );

    if (exitElement) {
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window
      });

      exitElement.dispatchEvent(clickEvent);
    }
  };

  const menuList = useMemo(
    () => [
      {
        key: 'personalCenter',
        label: '我的账户',
        clickHandler: () => window.open('https://console.bce.baidu.com/iam/#/iam/baseinfo', '_blank')
      },
      {
        key: 'billing',
        label: '财务中心',
        clickHandler: () => window.open('https://console.bce.baidu.com/billing/#/account/index', '_blank')
      },
      {
        key: 'security',
        label: '安全中心',
        clickHandler: () => window.open('https://console.bce.baidu.com/iam/#/iam/accesslist', '_blank')
      },
      {
        key: 'multiUser',
        label: '多用户访问控制',
        clickHandler: () => window.open('https://console.bce.baidu.com/iam/#/iam/overview', '_blank')
      }
    ],
    []
  );

  // 右侧信息栏
  const rightInfo = useMemo(() => {
    return (
      <>
        <div className={styles['right-info']}>
          <div className={styles['right-info-dropdown']}>
            {infoList.map((item) => (
              <div key={item.name} className={styles['right-info-dropdown-item']}>
                <Dropdown overlay={RightMenu({items: item.children})}>
                  <IconSvg type={item.icon} size={28}></IconSvg>
                </Dropdown>
              </div>
            ))}
          </div>
          <AvatarDropdown
            userName={decodeURIComponent(userName || '') || '百度智能云用户'}
            header
            logout={accountLogout}
            menuList={menuList}
          />
        </div>
      </>
    );
  }, [currentRegion.label, menuList, userName]);

  return (
    <div className={cx(styles['workspace-header'], styles['db-workspace-header'])}>
      <Menu
        mode="horizontal"
        scope="global"
        className={styles['menu']}
        headerMenu={WorkspaceSelect}
        otherArea={rightInfo}
        titleInfo={titleInfo}
        logoInfo={{logo: undefined}}
      />
    </div>
  );
};

export default Header;
